const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:8000/api/v1';
const TEST_EMPLOYER_ID = '1'; // From database query

async function testProfilePictureFlow() {
  console.log('🧪 Testing Profile Picture Data Flow\n');

  try {
    // Test 1: Fetch current employer details (Settings API)
    console.log('📋 Test 1: Fetching employer details via settings API...');
    const settingsResponse = await fetch(`${API_BASE}/getEmployerDetails`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': TEST_EMPLOYER_ID
      }
    });

    if (settingsResponse.ok) {
      const settingsData = await settingsResponse.json();
      console.log('✅ Settings API Response:');
      console.log(`   - Name: ${settingsData.data.emp_name}`);
      console.log(`   - Email: ${settingsData.data.emp_email}`);
      console.log(`   - Profile Picture: ${settingsData.data.profile_picture || 'None'}`);
      
      if (settingsData.data.profile_picture) {
        const fullImageUrl = `http://localhost:8000/${settingsData.data.profile_picture}`;
        console.log(`   - Full Image URL: ${fullImageUrl}`);
        
        // Test image accessibility
        try {
          const imageResponse = await fetch(fullImageUrl);
          console.log(`   - Image Accessible: ${imageResponse.ok ? '✅ Yes' : '❌ No'} (${imageResponse.status})`);
        } catch (error) {
          console.log(`   - Image Accessible: ❌ Error - ${error.message}`);
        }
      }
    } else {
      console.log('❌ Settings API failed:', settingsResponse.status, settingsResponse.statusText);
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 2: Fetch employer data via dashboard API
    console.log('📊 Test 2: Fetching employer data via dashboard API...');
    const dashboardResponse = await fetch(`${API_BASE}/employerProfilesData`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': TEST_EMPLOYER_ID
      }
    });

    if (dashboardResponse.ok) {
      const dashboardData = await dashboardResponse.json();
      console.log('✅ Dashboard API Response:');
      console.log(`   - Name: ${dashboardData.data.emp_name}`);
      console.log(`   - Profile Picture: ${dashboardData.data.profile_picture || 'None'}`);
      console.log(`   - Plan: ${dashboardData.data.plan_name}`);
      console.log(`   - Credits: ${dashboardData.data.creditsLeft}`);
      
      if (dashboardData.data.profile_picture) {
        const fullImageUrl = `http://localhost:8000/${dashboardData.data.profile_picture}`;
        console.log(`   - Full Image URL: ${fullImageUrl}`);
      }
    } else {
      console.log('❌ Dashboard API failed:', dashboardResponse.status, dashboardResponse.statusText);
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 3: Test profile picture update API (without actually uploading)
    console.log('🔄 Test 3: Testing profile picture update API structure...');
    
    // Create a test FormData to see what the API expects
    const testFormData = new FormData();
    testFormData.append('emp_name', 'Test User');
    testFormData.append('emp_email', '<EMAIL>');
    testFormData.append('emp_mobile', '1234567890');
    testFormData.append('designation', 'Test Designation');
    testFormData.append('company_name', 'Test Company');
    
    // Don't actually upload a file, just test the API structure
    console.log('📝 FormData structure prepared (without file upload)');
    console.log('   - emp_name: Test User');
    console.log('   - emp_email: <EMAIL>');
    console.log('   - emp_mobile: 1234567890');
    console.log('   - designation: Test Designation');
    console.log('   - company_name: Test Company');
    console.log('   - profile_picture: [Would be file if uploading]');

    console.log('\n' + '='.repeat(60) + '\n');

    // Test 4: Check if profile picture files exist on disk
    console.log('💾 Test 4: Checking profile picture files on disk...');
    
    const profilePicsDir = path.join(__dirname, 'visume-api', 'utils', 'files', 'profile_pics');
    console.log(`   - Profile pics directory: ${profilePicsDir}`);
    
    try {
      if (fs.existsSync(profilePicsDir)) {
        const files = fs.readdirSync(profilePicsDir);
        const imageFiles = files.filter(file => 
          file.includes('profile_picture') && 
          (file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.jpeg'))
        );
        
        console.log(`   - Total files in directory: ${files.length}`);
        console.log(`   - Profile picture files: ${imageFiles.length}`);
        
        if (imageFiles.length > 0) {
          console.log('   - Recent profile pictures:');
          imageFiles.slice(-3).forEach(file => {
            const filePath = path.join(profilePicsDir, file);
            const stats = fs.statSync(filePath);
            console.log(`     * ${file} (${Math.round(stats.size / 1024)}KB, ${stats.mtime.toLocaleString()})`);
          });
        }
      } else {
        console.log('   - ❌ Profile pics directory does not exist');
      }
    } catch (error) {
      console.log(`   - ❌ Error checking files: ${error.message}`);
    }

    console.log('\n' + '='.repeat(60) + '\n');
    console.log('🎯 Summary:');
    console.log('   - Settings API: Tests employer profile fetching');
    console.log('   - Dashboard API: Tests dashboard data fetching');
    console.log('   - File System: Verifies profile pictures exist on disk');
    console.log('   - Next Step: Test actual UI functionality in browser');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testProfilePictureFlow();
