// visume-api/routes/geminiRoutes.js
const express = require("express");
const router = express.Router();
const { GoogleGenerativeAI } = require("@google/generative-ai");


router.post("/assist", async (req, res) => {
  try {
    const { messages } = req.body;
    if (!Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: "Messages array is required" });
    }
    // Convert messages to Gemini SDK format
    const geminiMessages = messages.map((msg) => ({
      role: msg.role,
      parts: [{ text: msg.text }],
    }));
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    const result = await model.generateContent({ contents: geminiMessages });
    const text =
      result?.response?.candidates?.[0]?.content?.parts?.[0]?.text || "";
    res.json({
      candidates: [
        {
          content: {
            parts: [{ text }],
          },
        },
      ],
    });
  } catch (err) {
    console.error("Gemini SDK error:", err.message, err?.response?.data);
    res.status(500).json({ error: "Gemini SDK error", details: err?.response?.data || err.message });
  }
});
// Generate professional summary for a candidate using Gemini
router.post("/generate-summary", async (req, res) => {
  try {
    const { cand_id } = req.body;
    if (!cand_id) {
      return res.status(400).json({ error: "cand_id is required" });
    }

    // Dynamically import Prisma client
    const { PrismaClient } = require("@prisma/client");
    const prisma = new PrismaClient();

    // Fetch candidate profile and stripped_resume
    const candidate = await prisma.jobseeker.findUnique({
      where: { cand_id: cand_id },
      select: {
        cand_id: true,
        cand_name: true,
        cand_email: true,
        cand_mobile: true,
        preferred_location: true,
        profile_picture: true,
        stripped_resume: true,
      },
    });

    if (!candidate) {
      return res.status(404).json({ error: "Candidate not found" });
    }

    // Prepare prompt for Gemini
    let resumeText = "";
    if (candidate.stripped_resume) {
      try {
        const parsed = typeof candidate.stripped_resume === "string"
          ? JSON.parse(candidate.stripped_resume)
          : candidate.stripped_resume;
        resumeText = JSON.stringify(parsed, null, 2);
      } catch {
        resumeText = candidate.stripped_resume;
      }
    }

    const prompt = `
You are an expert resume writer. Write a concise, professional summary for the following candidate based on their resume and profile details. Use 3-5 sentences, highlight key skills, experience, and strengths, and avoid generic statements.

Candidate Name: ${candidate.cand_name}
Preferred Location: ${candidate.preferred_location}
Skills: ${candidate.skills}
Experience: ${candidate.experience}
Education: ${candidate.education}
Resume Data: ${resumeText}
`;

    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [{ text: prompt }],
        },
      ],
    });
    const summary =
      result?.response?.candidates?.[0]?.content?.parts?.[0]?.text || "";

    res.json({ summary });
  } catch (err) {
    console.error("Gemini summary error:", err.message, err?.response?.data);
    res
      .status(500)
      .json({ error: "Gemini summary error", details: err?.response?.data || err.message });
  }
});

module.exports = router;