const express = require("express");
const router = express.Router();
const userController = require("../controllers/userController");
const employerController = require("../controllers/employerController");
const videoProfileController = require("../controllers/videoProfileController");
const jobSeekerPlanController = require("../controllers/jobSeekerPlanController");
const employerProfileManager = require("../controllers/employer/employerProfileManager");
const employerShortlistController = require("../controllers/employer/employerShortlistController");
const jobDescriptionController = require("../controllers/employer/jobDescriptionController");
const companyController = require("../controllers/companyController");
const helper = require("../utils/helpers");
const multer = require("multer");
const path = require("path");

/**
 * Multer setup for file uploads (resume and profile pictures)
 * - Only allows jpg, jpeg, png for profile_picture (max 5MB)
 * - Only allows pdf for resume (max 10MB)
 */
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const dir =
      file.fieldname === "resume"
        ? "utils/files/resumes/"
        : file.fieldname === "job_description"
        ? "utils/files/job_description/"
        : "utils/files/profile_pics/";
    cb(null, dir);
  },
  filename: (req, file, cb) => {
    const ext = path.extname(file.originalname);
    cb(null, `${Date.now()}_${file.fieldname}${ext}`);
  },
});

function fileFilter(req, file, cb) {
  if (file.fieldname === "profile_picture") {
    // Accept only jpg, jpeg, png
    if (!file.originalname.match(/\.(jpg|jpeg|png)$/i)) {
      return cb(new Error("Only JPG, JPEG, and PNG files are allowed for profile picture"), false);
    }
    if (file.size > 5 * 1024 * 1024) {
      return cb(new Error("Profile picture must be less than 5MB"), false);
    }
  }
  if (file.fieldname === "resume") {
    // Accept only pdf
    if (!file.originalname.match(/\.pdf$/i)) {
      return cb(new Error("Only PDF files are allowed for resume"), false);
    }
    if (file.size > 10 * 1024 * 1024) {
      return cb(new Error("Resume must be less than 10MB"), false);
    }
  }
  cb(null, true);
}

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max for any file, but checked per field above
  },
});

// User authentication routes
router.post("/login-jobseeker", userController.loginJobseeker);
router.post("/login-employer", userController.loginEmployer);
router.post("/login-google", userController.loginGoogle);

// Email uniqueness check route
router.post("/check-email", userController.checkEmailUniqueness);

// Employer email uniqueness check route
router.post("/check-employer-email", employerController.checkEmployerEmailUniqueness);

// Register job seeker with resume and profile picture uploads
router.post(
  "/register-jobseeker",
  upload.fields([
    { name: "resume", maxCount: 1 },
    { name: "profile_picture", maxCount: 1 },
  ]),
  (err, req, res, next) => {
    // Multer error handler for file validation
    if (err instanceof multer.MulterError || err) {
      return res.status(400).json({ message: err.message });
    }
    next();
  },
  userController.registerJobseeker
);

router.post(
  "/upload-job-description/:emp_id",
  upload.fields([{ name: "job_description", maxCount: 1 }]),
  jobDescriptionController.uploadJobDescription
);

router.post(
  "/create-job-description",
  jobDescriptionController.createJobDescription
  );
  
router.get(
  "/job-description/:emp_id",

  jobDescriptionController.getJobDescriptionByEmployer
);

router.delete(
  "/job-description/:id",
  jobDescriptionController.deleteJobDescriptionById
);


router.post(
  "/add-new-company",
  upload.fields([{ name: "company_logo", maxCount: 1 }]),
  companyController.addNewCompany
);

router.get("/get-all-company", companyController.getAllCompany);

// Register job seeker with resume and profile picture uploads

router.post(
  "/register-employeer",
  upload.fields([{ name: "profile_picture", maxCount: 1 }]),
  (err, req, res, next) => {
    if (err instanceof multer.MulterError || err) {
      return res.status(400).json({ message: err.message });
    }
    next();
  },
  userController.registerEmployeer
);

// Upload a new resume for an existing jobseeker
router.post(
  "/upload-resume",
  upload.fields([{ name: "resume", maxCount: 1 }]),
  userController.uploadResume
);

// Route for creating a video profile
router.post("/create-video-resume", videoProfileController.createVideoProfile);

// Route for fetching all video profiles
router.get("/video-resume", videoProfileController.listVideoProfiles);

// Route for fetching video profiles by candidate ID
router.get(
  "/video-resume/:cand_id",
  videoProfileController.listVideoProfilesByCandidateId
);

// Route for fetching All candidate profiles
router.get("/getAllCandidates", videoProfileController.listAllCandidates);

router.get(
  "/getSuggestedCandidates",
  videoProfileController.listSuggestedCandidates
);

router.get("/suggestedJobs", userController.suggestedJobs);
// Route for fetching All candidate profiles
router.get("/filterCandidate", videoProfileController.filterCandidate);

// Route for fetching candidate profile and video resumes by candidate ID
router.get(
  "/candidate/:cand_id",
  videoProfileController.listCandidateAndVideoProfilesByCandidateId
);

// Candidate profile update (PUT)
router.put(
  "/candidate/:cand_id",
  userController.updateCandidateProfile
);

// Candidate profile image upload (POST)
router.post(
  "/candidate/uploadProfileImage",
  upload.fields([{ name: "profile_picture", maxCount: 1 }]),
  userController.uploadCandidateProfileImage
);

// Candidate resume upload (POST)
router.post(
  "/candidate/uploadResume",
  upload.fields([{ name: "resume", maxCount: 1 }]),
  userController.uploadResume
);

// Candidate notification settings update (PUT)
router.put(
  "/candidate/:cand_id/notifications",
  userController.updateCandidateNotifications
);

// Candidate privacy settings update (PUT)
router.put(
  "/candidate/:cand_id/privacy",
  userController.updateCandidatePrivacy
);

// Route for deleting video profiles by candidate ID
router.delete(
  "/video-resume/:video_profile_id",
  videoProfileController.deleteVideoProfileById
);

// Route for fetch video profile Data by Video Profile Id
router.get(
  "/video-resume-data/:video_profile_id",
  videoProfileController.fetchVideoProfileById
);

// Route for fetching job seeker plan by candidate ID
router.get(
  "/jobseeker-plan/:cand_id",
  jobSeekerPlanController.getJobSeekerPlanByCandidateId
);

// Route for fetching candidate membership status and Visume limits
router.get(
  "/candidate-membership/:cand_id",
  videoProfileController.getCandidateMembershipStatus
);

// 🎯 EMPLOYER MEMBERSHIP: Route for checking employer's membership status and credit information
router.get(
  "/employer-membership/:emp_id",
  employerController.getEmployerMembershipStatus
);

// Route for fetching profiles by employer ID
router.get(
  "/employer-profiles/:emp_id",
  employerProfileManager.getProfilesByEmployerId
);

// Route for shortlisting a video profile for an employer
router.post(
  "/employer-profiles/shortlist-candidate",
  employerShortlistController.shortlistVideoProfile
);

// Route for unshortlisting a video profile for an employer
router.post(
  "/employer-profiles/unshortlist-candidate",
  employerShortlistController.unShortlistVideoProfile
);

// Route for fetching shortlisting candidate by employer ID
router.get(
  "/shortlisted-profiles/:emp_id",
  employerShortlistController.getShortlistProfiles
);

router.get(
  "/api/v1/shortlisted-profiles/:emp_id",
  employerShortlistController.getShortlistProfiles
);

// Route for unlocking a video profile for an employer after being shortlisted
router.post(
  "/employer-profiles/unlockVideoProfile",
  employerShortlistController.unlockVideoProfile
);

// Route for deleting a video profile for an employer from shortlisted
router.delete(
  "/employer-profiles/remove",
  employerShortlistController.removeVideoProfile
);

router.get(
  "/employerProfilesData",
  employerProfileManager.getEmployerProfilesData
);


router.get("/get-roles", userController.getRoles);
router.get("/get-skills", userController.getSkills);
router.post("/recommend-skills", userController.recommendSkills);


router.get("/getEmployerDetails", employerProfileManager.getEmployerDetails);

// Update employer profile
router.put(
  "/updateEmployerProfile",
  upload.fields([{ name: "company_logo", maxCount: 1 }]),
  employerProfileManager.updateEmployerProfile
);

router.post("/candidate/analytics", videoProfileController.addAnalytics);

router.put("/add-video-resume", videoProfileController.addVideoProfileData);
router.put("/finish-video-resume", videoProfileController.finishVideoProfile);
router.put("/videoprofile/:vpid/questions", videoProfileController.updateVideoProfileQuestions);
router.put("/changePassword", userController.changePassword);

router.post("/generateScore", videoProfileController.generateScore);


module.exports = router;
