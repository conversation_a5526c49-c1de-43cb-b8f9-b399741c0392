/**
 * Test script for employer registration flow
 * This script tests the modified registration system to ensure it works with personal information only
 */

const API_BASE_URL = process.env.VITE_APP_HOST || 'http://localhost:3001';

// Test data for registration
const testEmployerData = {
  emp_name: "<PERSON>",
  emp_email: `test.employer.${Date.now()}@example.com`, // Unique email
  password: "testpassword123",
  emp_mobile: "9876543210",
  designation: "Software Engineer"
};

// Test functions
async function testEmailUniqueness() {
  console.log('🧪 Testing email uniqueness check...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/check-employer-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email: testEmployerData.emp_email })
    });

    const result = await response.json();
    
    if (response.ok && result.available) {
      console.log('✅ Email uniqueness check passed');
      return true;
    } else {
      console.log('❌ Email uniqueness check failed:', result.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Email uniqueness check error:', error.message);
    return false;
  }
}

async function testEmployerRegistration() {
  console.log('🧪 Testing employer registration...');
  
  try {
    const formData = new FormData();
    formData.append('emp_email', testEmployerData.emp_email);
    formData.append('email', testEmployerData.emp_email); // For user table uniqueness
    formData.append('password', testEmployerData.password);
    formData.append('emp_name', testEmployerData.emp_name);
    formData.append('emp_mobile', testEmployerData.emp_mobile);
    formData.append('designation', testEmployerData.designation);

    const response = await fetch(`${API_BASE_URL}/api/v1/register-employeer`, {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Employer registration successful');
      console.log('📋 Registration result:', {
        message: result.message,
        role: result.role,
        emp_id: result.emp_id,
        employerId: result.employerId
      });
      return { success: true, data: result };
    } else {
      console.log('❌ Employer registration failed:', result.message);
      return { success: false, error: result.message };
    }
  } catch (error) {
    console.error('❌ Employer registration error:', error.message);
    return { success: false, error: error.message };
  }
}

async function testEmployerLogin(email, password) {
  console.log('🧪 Testing employer login...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/login-employer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password })
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Employer login successful');
      return { success: true, data: result };
    } else {
      console.log('❌ Employer login failed:', result.message);
      return { success: false, error: result.message };
    }
  } catch (error) {
    console.error('❌ Employer login error:', error.message);
    return { success: false, error: error.message };
  }
}

async function testGetEmployerDetails(empId) {
  console.log('🧪 Testing get employer details...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/getEmployerDetails`, {
      method: 'GET',
      headers: {
        'Authorization': empId.toString(),
        'Content-Type': 'application/json',
      }
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Get employer details successful');
      console.log('📋 Employer details:', {
        emp_name: result.data.emp_name,
        emp_email: result.data.emp_email,
        emp_mobile: result.data.emp_mobile,
        designation: result.data.designation,
        company_id: result.data.company_id,
        profile_picture: result.data.profile_picture
      });
      return { success: true, data: result.data };
    } else {
      console.log('❌ Get employer details failed:', result.message);
      return { success: false, error: result.message };
    }
  } catch (error) {
    console.error('❌ Get employer details error:', error.message);
    return { success: false, error: error.message };
  }
}

// Main test runner
async function runRegistrationFlowTest() {
  console.log('🚀 Starting Employer Registration Flow Test');
  console.log('=' .repeat(50));
  
  // Step 1: Test email uniqueness
  const emailCheck = await testEmailUniqueness();
  if (!emailCheck) {
    console.log('❌ Test failed at email uniqueness check');
    return;
  }
  
  // Step 2: Test registration
  const registration = await testEmployerRegistration();
  if (!registration.success) {
    console.log('❌ Test failed at registration');
    return;
  }
  
  // Step 3: Test login
  const login = await testEmployerLogin(testEmployerData.emp_email, testEmployerData.password);
  if (!login.success) {
    console.log('❌ Test failed at login');
    return;
  }
  
  // Step 4: Test get employer details
  const details = await testGetEmployerDetails(registration.data.employerId);
  if (!details.success) {
    console.log('❌ Test failed at get employer details');
    return;
  }
  
  // Verify data integrity
  console.log('🧪 Verifying data integrity...');
  const detailsData = details.data;
  
  if (detailsData.emp_name === testEmployerData.emp_name &&
      detailsData.emp_email === testEmployerData.emp_email &&
      detailsData.emp_mobile.toString() === testEmployerData.emp_mobile &&
      detailsData.designation === testEmployerData.designation) {
    console.log('✅ Data integrity check passed');
  } else {
    console.log('❌ Data integrity check failed');
    console.log('Expected:', testEmployerData);
    console.log('Actual:', {
      emp_name: detailsData.emp_name,
      emp_email: detailsData.emp_email,
      emp_mobile: detailsData.emp_mobile.toString(),
      designation: detailsData.designation
    });
    return;
  }
  
  console.log('=' .repeat(50));
  console.log('🎉 All tests passed! Registration flow is working correctly.');
  console.log('✅ Personal information-focused registration system is functional');
  console.log('✅ No company information is required during registration');
  console.log('✅ Data is stored and retrieved correctly');
}

// Run the test if this script is executed directly
if (require.main === module) {
  runRegistrationFlowTest().catch(console.error);
}

module.exports = {
  runRegistrationFlowTest,
  testEmailUniqueness,
  testEmployerRegistration,
  testEmployerLogin,
  testGetEmployerDetails
};
