// index.jsx
import React, { useEffect, useState } from "react";
import { HiOutlineUserGroup, HiOutlineVideoCamera } from "react-icons/hi";
import Cookies from "js-cookie";
import ProfileSkelLoader from "../components/ProfileSkelLoader";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import ProfileCard from "./ProfileCard";
import SearchBar from "./SearchBar";

const ProfileSearchUI = () => {
  const navigate = useNavigate();
  const [pageSize] = useState(10);
  const [showLoadMore, setSetShowLoadMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const [totalProfiles, setTotalProfiles] = useState(500);
  const [selectedProfiles, setSelectedProfiles] = useState(new Set());
  const [profiles, setProfiles] = useState([]);
  const [loadingId, setLoadingId] = useState(null);
  const [shortListedProfiles, setShortListedProfiles] = useState([]);
  const [oldProfiles, setOldProfiles] = useState([]);
  const [viewInteraction, setViewInteraction] = useState(0);

  const handleShortlist = async (id, cand_id) => {
    if (loadingId === id) return;
    setLoadingId(id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        return toast.error("You need to be an employer to shortlist profiles");
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast((t) => (
        <span className="flex items-center">
          <div className="mr-[10px]">
            <i className="fas fa-check-circle text-[#28a745]"></i>
          </div>
          <div className="flex items-start justify-start gap-2">
            {data.message}
            <button
              onClick={() => {
                toast.dismiss(t.id);
                navigate("/employer/track-candidates");
              }}
              className="ml-[10px] cursor-pointer rounded-[4px] border-0 bg-[#28a745] px-2 py-1 text-[#fff]"
            >
              Shortlisted Candidates
            </button>
          </div>
        </span>
      ));
      setSelectedProfiles((prev) => {
        const newSelectedProfiles = new Set(prev);
        if (newSelectedProfiles.has(id)) {
          newSelectedProfiles.delete(id);
        } else {
          newSelectedProfiles.add(id);
        }
        return newSelectedProfiles;
      });
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const getAllProfiles = async (pageLength) => {
    if (profiles.length < totalProfiles) {
      try {
        const dummyProfiles = Array.from({ length: 10 }, (_, index) => ({
          name: `Loading... ${index}`,
        }));

        setProfiles((prev) => [...prev, ...dummyProfiles]);

        const page = Math.ceil(profiles.length / 10);
        const data = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/getAllCandidates?page=${
            pageLength ? pageLength : page + 1
          }&pageSize=${pageSize}`
        );
        const res = await data.json();

        setTotalProfiles(res.pagination.totalCandidates);
        setProfiles((prev) => {
          const newProfiles = prev.slice(0, prev.length - 10);
          return [...newProfiles, ...res.candidateProfiles];
        });

        setSetShowLoadMore(true);
      } catch (err) {
        console.log(`Error`, err);
      }
    }
  };

  const getShortListedProfiles = async () => {
    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        toast.error("You are not an employeer");
        navigate("/");
        return;
      }
      fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/shortlisted-profiles/${emp_id}`
      )
        .then((res) => res.json())
        .then((data) => {
          if (data.data && data.data.length) {
            const shortIds = [];
            data.data.map((e) => shortIds.push(e.video_profile_id));
            setShortListedProfiles(shortIds);
          }
        });
    } catch (err) {
      console.log(`Error`, err);
    }
  };

  // useEffect for getShortListedProfiles removed as per request

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 via-white to-gray-50 pb-8 ">
      <div
        className={`container mx-auto ${
          !viewInteraction &&
          "flex h-[60vh] flex-col items-center justify-center gap-8"
        }`}
      >
        {!viewInteraction && (
          <div className="mt-40 p-4 text-center md:p-0 ">
            <h1 className="flex items-center justify-center gap-2 text-2xl font-extrabold tracking-tight text-gray-900 md:text-4xl">
              Search Video Resumes <HiOutlineVideoCamera />
            </h1>
            <p className="mx-auto mt-4 max-w-2xl text-sm text-gray-600 md:text-lg">
              Discover talented candidates by exploring their AI-powered video
              resumes.
              <br />
              Use advanced search tools to refine results.
            </p>
          </div>
        )}

        <SearchBar
          setLoading={setLoading}
          setOldProfiles={setOldProfiles}
          oldProfiles={oldProfiles}
          loading={loading}
          setProfiles={setProfiles}
          setSetShowLoadMore={setSetShowLoadMore}
          getAllProfiles={getAllProfiles}
          setViewInteraction={setViewInteraction}
        />

        {viewInteraction ? (
          <>
            {/* Profile Grid */}
            <div className="mt-12 px-6">
              <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {profiles && profiles.length > 0 ? (
                  profiles.map((profile) =>
                    profile.id ? (
                      <ProfileCard
                        shortListedProfiles={shortListedProfiles}
                        key={profile.id}
                        keyValue={profile.id}
                        {...profile}
                        isShortlisted={selectedProfiles.has(profile.id)}
                        onShortlist={handleShortlist}
                        isLoading={loadingId === profile.id}
                        className="transform overflow-hidden rounded-xl bg-white shadow-lg transition duration-300 hover:scale-105 hover:shadow-2xl"
                      />
                    ) : (
                      <ProfileSkelLoader key={`skeleton-${Math.random()}`} />
                    )
                  )
                ) : (
                  <div className="col-span-full flex flex-col items-center justify-center py-20 text-gray-500">
                    <HiOutlineUserGroup className="mb-6 animate-pulse text-5xl text-gray-400" />
                    <p className="text-center text-xl font-semibold">
                      No profiles found
                    </p>
                    <p className="mt-1 text-center text-sm text-gray-500">
                      Try different search parameters to find candidates.
                    </p>
                  </div>
                )}
              </div>
              {/* Load More Button */}
              {profiles && profiles.length < totalProfiles && showLoadMore && (
                <div className="mt-12 flex justify-center">
                  <button
                    onClick={() => getAllProfiles()}
                    className="relative inline-flex items-center justify-center overflow-hidden rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-4 text-sm font-semibold text-white shadow-lg transition-transform duration-300 hover:scale-105"
                  >
                    <span className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700 blur-md"></span>
                    <span className="relative z-10 flex items-center space-x-2">
                      <HiOutlineUserGroup className="text-lg" />
                      <span>Load More Profiles</span>
                    </span>
                  </button>
                </div>
              )}
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default ProfileSearchUI;
export { default as ProfileCard } from "./ProfileCard";
export { default as SearchBar } from "./SearchBar";
